-- Migration: Add strategic theme generation logging support
-- Date: 2025-01-04
-- Description: Add fields to support strategic theme generation logging in ai_analysis_logs table

-- Add new columns for strategic theme generation
ALTER TABLE ai_analysis_logs 
ADD COLUMN themes_generated INTEGER DEFAULT 0,
ADD COLUMN custom_prompt TEXT,
ADD COLUMN generation_config JSONB,
ADD COLUMN confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1);

-- Update operation_type constraint to include strategic_theme_generation
ALTER TABLE ai_analysis_logs 
DROP CONSTRAINT IF EXISTS ai_analysis_logs_operation_type_check;

ALTER TABLE ai_analysis_logs 
ADD CONSTRAINT ai_analysis_logs_operation_type_check 
CHECK (operation_type IN ('health_check', 'single_analysis', 'batch_analysis', 'strategic_theme_generation'));

-- Add comments for documentation
COMMENT ON COLUMN ai_analysis_logs.themes_generated IS 'Number of strategic themes generated (typically 3)';
COMMENT ON COLUMN ai_analysis_logs.custom_prompt IS 'Custom user prompt for theme generation';
COMMENT ON COLUMN ai_analysis_logs.generation_config IS 'JSON configuration used for theme generation';
COMMENT ON COLUMN ai_analysis_logs.confidence_score IS 'AI confidence score (0.0-1.0) for the generated themes';

-- Update the summary view to include strategic theme fields
DROP VIEW IF EXISTS ai_analysis_logs_summary;

CREATE VIEW ai_analysis_logs_summary AS
SELECT 
    id,
    session_id,
    operation_type,
    status,
    feature_count,
    meaningful_relationships_found,
    total_relationships_analyzed,
    themes_generated,
    confidence_score,
    error_message,
    duration_seconds,
    started_at,
    completed_at,
    CASE 
        WHEN status = 'completed' THEN '✅'
        WHEN status = 'failed' THEN '❌'
        WHEN status = 'in_progress' THEN '🔄'
        ELSE '⏳'
    END as status_icon,
    CASE 
        WHEN duration_seconds IS NOT NULL THEN duration_seconds || 's'
        WHEN status = 'in_progress' THEN 'Running...'
        ELSE 'Pending'
    END as duration_display,
    CASE 
        WHEN operation_type = 'strategic_theme_generation' THEN 
            COALESCE(themes_generated::text || ' themes', 'Theme generation')
        WHEN operation_type = 'batch_analysis' THEN 
            COALESCE(meaningful_relationships_found::text || ' relationships', 'Batch analysis')
        WHEN operation_type = 'single_analysis' THEN 'Single analysis'
        ELSE 'Health check'
    END as operation_summary
FROM ai_analysis_logs
ORDER BY created_at DESC;

-- Add index for performance on new columns
CREATE INDEX idx_ai_logs_operation_themes ON ai_analysis_logs(operation_type, themes_generated);
CREATE INDEX idx_ai_logs_confidence ON ai_analysis_logs(confidence_score);

-- Update table comment
COMMENT ON TABLE ai_analysis_logs IS 'Comprehensive logging for all AI analysis operations including strategic theme generation';
