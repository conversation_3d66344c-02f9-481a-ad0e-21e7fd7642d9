'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  ChevronLeft,
  Brain,
  Loader2,
  Target,
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Settings,
  RefreshCw,
  GitCompare,
  Eye,
  Settings
} from 'lucide-react'
import Breadcrumbs from '@/components/Breadcrumbs'
import ThemeDetailModal from '@/components/ThemeDetailModal'
import { ThemeAnalysisResult, StrategicTheme } from '@/lib/types/themes'

interface ThemeCardProps {
  theme: StrategicTheme
  index: number
  onSelect: (theme: StrategicTheme) => void
}

function ThemeCard({ theme, index, onSelect }: ThemeCardProps) {
  const themeColors = [
    'border-blue-200 bg-blue-50',
    'border-green-200 bg-green-50', 
    'border-purple-200 bg-purple-50'
  ]

  const headerColors = [
    'bg-blue-600',
    'bg-green-600',
    'bg-purple-600'
  ]

  return (
    <div className={`card ${themeColors[index]} border-2 hover:shadow-2xl transition-all duration-300 cursor-pointer`}
         onClick={() => onSelect(theme)}>
      <div className={`${headerColors[index]} text-white p-4 -m-6 mb-4 rounded-t-xl`}>
        <h3 className="text-xl font-bold">{theme.name}</h3>
        <p className="text-sm opacity-90 mt-1">{theme.corePhilosophy}</p>
      </div>

      <div className="space-y-4">
        {/* Strategic Approach */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <Target className="w-4 h-4 mr-2" />
            Strategic Approach
          </h4>
          <p className="text-sm text-gray-600">{theme.strategicApproach}</p>
        </div>

        {/* Target Segments */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Target Segments
          </h4>
          <div className="space-y-1">
            {theme.targetUserSegments.slice(0, 2).map((segment, idx) => (
              <div key={idx} className="text-sm">
                <span className="font-medium">{segment.name}:</span>
                <span className="text-gray-600 ml-1">{segment.description}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Key Features Count */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-2">Key Features</h4>
          <p className="text-sm text-gray-600">
            {theme.keyFeatures.length} features selected
            <span className="ml-2 text-xs bg-gray-200 px-2 py-1 rounded">
              {theme.keyFeatures.filter(f => f.priority === 'high').length} high priority
            </span>
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-800">{theme.expectedROI}</div>
            <div className="text-xs text-gray-600">Expected ROI</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-800">{theme.totalEstimatedCost}</div>
            <div className="text-xs text-gray-600">Estimated Cost</div>
          </div>
        </div>

        {/* AARRR Focus */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-2">Primary Focus</h4>
          <div className="flex flex-wrap gap-1">
            {theme.aarrStageFocus
              .filter(stage => stage.impactLevel === 'primary')
              .map((stage, idx) => (
                <span key={idx} className="text-xs bg-neo-cyan text-neo-dark px-2 py-1 rounded-full">
                  {stage.stage.toUpperCase()}
                </span>
              ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function StrategicThemesPage() {
  const [data, setData] = useState<ThemeAnalysisResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedTheme, setSelectedTheme] = useState<StrategicTheme | null>(null)
  const [showComparison, setShowComparison] = useState(false)
  const [customPrompt, setCustomPrompt] = useState('')
  const [showPromptInput, setShowPromptInput] = useState(false)

  const generateThemes = async () => {
    try {
      setLoading(true)
      setError(null)

      // Prepare request body with custom prompt if provided
      const requestBody = customPrompt.trim() ? {
        customPrompt: customPrompt.trim(),
        focusAreas: ['user_engagement', 'revenue_growth', 'market_differentiation'],
        businessObjectives: ['increase_user_acquisition', 'improve_retention', 'boost_revenue'],
        constraints: ['development_resources', 'timeline', 'technical_complexity'],
        timeHorizon: '12_months',
        budgetRange: 'medium'
      } : undefined

      const response = requestBody
        ? await fetch('/api/strategic-themes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
          })
        : await fetch('/api/strategic-themes')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate themes')
      }

      const result = await response.json()
      setData(result.data)
      setShowPromptInput(false) // Hide prompt input after successful generation
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate themes')
    } finally {
      setLoading(false)
    }
  }

  // Remove auto-generation on page load
  // useEffect(() => {
  //   generateThemes()
  // }, [])

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Generation Failed</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={generateThemes}
            className="btn-primary flex items-center mx-auto"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <div className="py-3 border-b border-gray-100">
            <Breadcrumbs />
          </div>
          
          {/* Page Header */}
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link 
                  href="/matrix"
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-5 h-5 text-gray-600" />
                </Link>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                    <Brain className="w-8 h-8 mr-3 text-neo-cyan" />
                    Strategic Theme Selection
                  </h1>
                  <p className="text-gray-600 mt-1">
                    AI-generated strategic approaches for gamification implementation
                  </p>
                </div>
              </div>
              
              <div className="flex space-x-3">
                {data && (
                  <button
                    onClick={() => setShowComparison(!showComparison)}
                    className="btn-secondary flex items-center"
                  >
                    <GitCompare className="w-4 h-4 mr-2" />
                    {showComparison ? 'Hide' : 'Compare'}
                  </button>
                )}
                <button
                  onClick={() => setShowPromptInput(!showPromptInput)}
                  disabled={loading}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  {showPromptInput ? 'Hide Options' : 'Custom Generate'}
                </button>
                <button
                  onClick={generateThemes}
                  disabled={loading}
                  className="btn-primary flex items-center"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Brain className="w-4 h-4 mr-2" />
                  )}
                  {loading ? 'Generating...' : data ? 'Regenerate' : 'Generate Themes'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Custom Prompt Input */}
      {showPromptInput && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2 text-purple-600" />
              Custom Generation Options
            </h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="customPrompt" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Instructions (Optional)
                </label>
                <textarea
                  id="customPrompt"
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="Add specific requirements, focus areas, or constraints for theme generation..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Example: "Focus on mobile-first features for Gen Z users" or "Prioritize low-cost, high-impact solutions"
                </p>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {customPrompt.length}/500 characters
                </span>
                <button
                  onClick={() => setCustomPrompt('')}
                  className="text-sm text-purple-600 hover:text-purple-700"
                >
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center py-16">
            <div className="text-center">
              <Loader2 className="w-12 h-12 text-neo-cyan animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Generating Strategic Themes
              </h3>
              <p className="text-gray-600">
                Analyzing {data?.themes?.length || 'all'} features to create strategic recommendations...
              </p>
            </div>
          </div>
        ) : !data ? (
          // Welcome state - no data loaded yet
          <div className="text-center py-16">
            <div className="max-w-2xl mx-auto">
              <Brain className="w-20 h-20 text-neo-cyan mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Strategic Theme Generation
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Generate AI-powered strategic themes for your gamification implementation.
                Our system will analyze all {/* feature count will be shown here */} features
                and create 3 distinct strategic approaches tailored for CEO decision-making.
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">What You'll Get:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">3 distinct strategic themes</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">Target user segments analysis</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">Implementation timelines</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">ROI and cost estimates</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">Risk assessment & mitigation</span>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-blue-800">Comparison matrix</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-4 justify-center">
                <button
                  onClick={() => setShowPromptInput(!showPromptInput)}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center"
                >
                  <Settings className="w-5 h-5 mr-2" />
                  Custom Options
                </button>
                <button
                  onClick={generateThemes}
                  disabled={loading}
                  className="btn-primary flex items-center text-lg px-8 py-4"
                >
                  <Brain className="w-6 h-6 mr-2" />
                  Generate Strategic Themes
                </button>
              </div>
            </div>
          </div>
        ) : data ? (
          <div className="space-y-8">
            {/* Executive Summary */}
            <div className="card bg-neo-dark text-white">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Executive Summary
              </h2>
              <p className="text-gray-300 mb-4">{data.executiveSummary}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-400">
                    Generated: {new Date(data.generatedAt).toLocaleDateString()}
                  </span>
                  <span className="text-sm text-gray-400">
                    Confidence: {Math.round(data.confidence * 100)}%
                  </span>
                </div>
                <CheckCircle className="w-5 h-5 text-green-400" />
              </div>
            </div>

            {/* Strategic Themes Grid */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Strategic Themes</h2>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {data.themes.map((theme, index) => (
                  <ThemeCard
                    key={theme.id}
                    theme={theme}
                    index={index}
                    onSelect={setSelectedTheme}
                  />
                ))}
              </div>
            </div>

            {/* Comparison Matrix */}
            {showComparison && data.comparison && (
              <div className="card">
                <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <GitCompare className="w-5 h-5 mr-2" />
                  Theme Comparison Matrix
                </h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Dimension
                        </th>
                        {data.themes.map((theme, index) => (
                          <th key={theme.id} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {theme.name}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.comparison.map((comparison, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {comparison.dimension}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-700">
                            {comparison.theme1Value}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-700">
                            {comparison.theme2Value}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-700">
                            {comparison.theme3Value}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">Analysis</h3>
                  <p className="text-blue-800 text-sm">{data.comparison[0]?.analysis}</p>
                </div>
              </div>
            )}

            {/* Recommendation */}
            <div className="card bg-green-50 border-green-200">
              <h2 className="text-xl font-bold text-green-900 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Recommendation
              </h2>
              <p className="text-green-800 mb-4">{data.recommendation}</p>
              {data.nextSteps && data.nextSteps.length > 0 && (
                <div>
                  <h3 className="font-semibold text-green-900 mb-2">Next Steps:</h3>
                  <ul className="text-green-800 list-disc list-inside space-y-1">
                    {data.nextSteps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        ) : null}
      </main>

      {/* Theme Detail Modal */}
      <ThemeDetailModal
        theme={selectedTheme}
        isOpen={!!selectedTheme}
        onClose={() => setSelectedTheme(null)}
      />
    </div>
  )
}
