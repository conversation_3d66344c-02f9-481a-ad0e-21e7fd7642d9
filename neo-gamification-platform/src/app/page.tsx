import Image from "next/image";
import Link from "next/link";
import { Gamepad2, Target, Users, TrendingUp, DollarSign, Brain } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-neo-dark border-b-4 border-neo-cyan">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src="/assets/neo-logo.svg"
                alt="NEO Bank Logo"
                width={60}
                height={60}
                className="filter brightness-0 invert"
              />
              <div>
                <h1 className="text-3xl font-bold text-neo-cyan">
                  NEO Bank Gamification Platform
                </h1>
                <p className="text-gray-300 mt-1">
                  Strategic AARRR Framework Implementation
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-white mb-6">
            Transform Banking with
            <span className="text-neo-cyan"> Gamification</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Comprehensive gamification strategy platform designed to enhance user engagement
            across the entire customer journey using the proven AARRR framework.
          </p>
          <div className="flex gap-4 justify-center flex-wrap">
            <Link href="/dashboard" className="btn-primary flex items-center">
              <Gamepad2 className="w-5 h-5 mr-2" />
              Launch Dashboard
            </Link>
            <Link href="/matrix" className="btn-secondary flex items-center">
              <Target className="w-5 h-5 mr-2" />
              View Matrix
            </Link>
            <Link href="/strategic-themes" className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center">
              <Brain className="w-5 h-5 mr-2" />
              Strategic Themes
            </Link>
          </div>
        </div>

        {/* AARRR Framework Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-16">
          {[
            {
              stage: "Acquisition",
              icon: Users,
              color: "bg-blue-500",
              description: "Attract new users through gamified onboarding and referral campaigns"
            },
            {
              stage: "Activation",
              icon: Target,
              color: "bg-green-500",
              description: "Engage users with achievement systems and tutorial rewards"
            },
            {
              stage: "Retention",
              icon: TrendingUp,
              color: "bg-purple-500",
              description: "Keep users active with daily rewards and progress tracking"
            },
            {
              stage: "Referral",
              icon: Users,
              color: "bg-pink-500",
              description: "Encourage sharing through social challenges and incentives"
            },
            {
              stage: "Revenue",
              icon: DollarSign,
              color: "bg-yellow-500",
              description: "Drive monetization with premium tiers and transaction rewards"
            },
          ].map((item, index) => (
            <div key={index} className="card text-center">
              <div className={`${item.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4`}>
                <item.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">{item.stage}</h3>
              <p className="text-gray-600 text-sm">{item.description}</p>
            </div>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="bg-white rounded-xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            Platform Overview
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-neo-cyan mb-2">27</div>
              <div className="text-gray-600">Gamification Features</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-neo-secondary mb-2">5</div>
              <div className="text-gray-600">AARRR Stages</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-neo-light mb-2">100%</div>
              <div className="text-gray-600">AI-Analyzed Impacts</div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-neo-dark border-t border-gray-700 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src="/assets/neo-contact-logo.png"
                alt="NEO Bank"
                width={40}
                height={40}
              />
              <div className="text-gray-300">
                <p className="font-semibold">NEO Bank Strategy Platform</p>
                <p className="text-sm">Powered by Next.js & Supabase</p>
              </div>
            </div>
            <div className="text-gray-400 text-sm">
              © 2024 NEO Bank. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
