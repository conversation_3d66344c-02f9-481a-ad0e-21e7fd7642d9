import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUserProfile } from '@/lib/auth'
import { generateStrategicThemes } from '@/lib/services/themeGenerator'
import { FeatureAnalysisInput, ThemeGenerationConfig } from '@/lib/types/themes'
import { createErrorResponse, createSuccessResponse } from '@/lib/validations/api'

// GET /api/strategic-themes - Generate strategic themes based on feature data
export async function GET(request: NextRequest) {
  try {
    // Simple auth check - just verify user is authenticated
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    const { searchParams } = new URL(request.url)
    
    // Parse optional configuration parameters
    const config: Partial<ThemeGenerationConfig> = {
      numberOfThemes: 3, // Fixed for now
      focusAreas: searchParams.get('focus_areas')?.split(',') || ['user_engagement', 'revenue_growth', 'market_differentiation'],
      businessObjectives: searchParams.get('objectives')?.split(',') || ['increase_user_acquisition', 'improve_retention', 'boost_revenue'],
      constraints: searchParams.get('constraints')?.split(',') || ['development_resources', 'timeline', 'technical_complexity'],
      timeHorizon: searchParams.get('time_horizon') || '12_months',
      budgetRange: searchParams.get('budget_range') || 'medium'
    }

    const supabase = await createClient()

    // Fetch all features with complete data
    const { data: features, error } = await supabase
      .from('features_complete')
      .select('*')
      .order('priority', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return createErrorResponse('Failed to fetch features data', 500)
    }

    if (!features || features.length === 0) {
      return createErrorResponse('No features data available for analysis', 404)
    }

    // Transform database features to analysis input format
    const analysisInput: FeatureAnalysisInput[] = features.map(feature => ({
      id: feature.id,
      name: feature.name,
      description: feature.description,
      category: feature.category,
      stage: feature.stage,
      priority: feature.priority,
      difficulty: feature.difficulty,
      research_backed: feature.research_backed,
      justification: feature.justification,
      examples: feature.examples || [],
      mechanics: feature.mechanics || [],
      impact_areas: feature.impact_areas || [],
      stage_impacts: feature.stage_impacts || []
    }))

    console.log(`🎯 Generating strategic themes from ${analysisInput.length} features`)
    console.log(`📊 Configuration:`, config)

    // Generate strategic themes using AI
    const themeAnalysis = await generateStrategicThemes(analysisInput, config as ThemeGenerationConfig)

    console.log(`✅ Successfully generated ${themeAnalysis.themes.length} strategic themes`)
    console.log(`🎯 Confidence score: ${themeAnalysis.confidence}`)

    return createSuccessResponse(themeAnalysis, 'Strategic themes generated successfully')

  } catch (error) {
    console.error('Strategic themes API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('OPENAI_API_KEY')) {
        return createErrorResponse('AI service not configured', 503)
      }
      if (error.message.includes('rate limit')) {
        return createErrorResponse('AI service rate limit exceeded. Please try again later.', 429)
      }
      if (error.message.includes('Failed to generate')) {
        return createErrorResponse(error.message, 500)
      }
    }
    
    return createErrorResponse('Internal server error', 500)
  }
}

// POST /api/strategic-themes - Generate themes with custom configuration
export async function POST(request: NextRequest) {
  try {
    // Simple auth check - just verify user is authenticated
    const profile = await getCurrentUserProfile()
    if (!profile) {
      return createErrorResponse('Authentication required', 401)
    }

    const body = await request.json()
    const config: ThemeGenerationConfig = {
      numberOfThemes: 3, // Fixed
      focusAreas: body.focusAreas || ['user_engagement', 'revenue_growth', 'market_differentiation'],
      businessObjectives: body.businessObjectives || ['increase_user_acquisition', 'improve_retention', 'boost_revenue'],
      constraints: body.constraints || ['development_resources', 'timeline', 'technical_complexity'],
      timeHorizon: body.timeHorizon || '12_months',
      budgetRange: body.budgetRange || 'medium'
    }

    // Add custom prompt to config if provided
    const customPrompt = body.customPrompt?.trim()
    if (customPrompt) {
      (config as any).customPrompt = customPrompt
    }

    const supabase = await createClient()

    // Fetch features with optional filtering
    let query = supabase
      .from('features_complete')
      .select('*')

    // Apply filters if provided
    if (body.priorityFilter) {
      query = query.eq('priority', body.priorityFilter)
    }
    if (body.difficultyFilter) {
      query = query.eq('difficulty', body.difficultyFilter)
    }
    if (body.stageFilter) {
      query = query.eq('stage', body.stageFilter)
    }

    query = query.order('priority', { ascending: false })

    const { data: features, error } = await query

    if (error) {
      console.error('Database error:', error)
      return createErrorResponse('Failed to fetch features data', 500)
    }

    if (!features || features.length === 0) {
      return createErrorResponse('No features data available for analysis', 404)
    }

    // Transform database features to analysis input format
    const analysisInput: FeatureAnalysisInput[] = features.map(feature => ({
      id: feature.id,
      name: feature.name,
      description: feature.description,
      category: feature.category,
      stage: feature.stage,
      priority: feature.priority,
      difficulty: feature.difficulty,
      research_backed: feature.research_backed,
      justification: feature.justification,
      examples: feature.examples || [],
      mechanics: feature.mechanics || [],
      impact_areas: feature.impact_areas || [],
      stage_impacts: feature.stage_impacts || []
    }))

    console.log(`🎯 Generating custom strategic themes from ${analysisInput.length} features`)
    console.log(`📊 Custom configuration:`, config)

    // Generate strategic themes using AI
    const themeAnalysis = await generateStrategicThemes(analysisInput, config)

    console.log(`✅ Successfully generated ${themeAnalysis.themes.length} strategic themes`)
    console.log(`🎯 Confidence score: ${themeAnalysis.confidence}`)

    return createSuccessResponse(themeAnalysis, 'Custom strategic themes generated successfully')

  } catch (error) {
    console.error('Strategic themes API error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('OPENAI_API_KEY')) {
        return createErrorResponse('AI service not configured', 503)
      }
      if (error.message.includes('rate limit')) {
        return createErrorResponse('AI service rate limit exceeded. Please try again later.', 429)
      }
      if (error.message.includes('Failed to generate')) {
        return createErrorResponse(error.message, 500)
      }
    }
    
    return createErrorResponse('Internal server error', 500)
  }
}
