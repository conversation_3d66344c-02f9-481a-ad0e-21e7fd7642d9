'use client'

import { useState, useEffect, Suspense } from 'react'
import Link from 'next/link'
import { ChevronLeft, Filter, Search, Eye, ExternalLink, Brain, Loader2, CheckCircle, AlertCircle, FileText, Target } from 'lucide-react'
import SplitView, { useSplitView } from '@/components/SplitView'
import FeatureDetails from '@/components/FeatureDetails'
import Breadcrumbs from '@/components/Breadcrumbs'

interface Feature {
  id: string
  feature_id: string
  name: string
  description: string
  category: string
  stage: string
  priority: 'low' | 'medium' | 'high'
  difficulty: 'low' | 'medium' | 'high'
  research_backed: boolean
  stage_impacts: Array<{
    stage: string
    impact_level: 'none' | 'low' | 'medium' | 'high'
    reasoning?: string
  }>
  examples?: Array<{
    company_name: string
    success_story: string
  }>
  mechanics?: string[]
  impact_areas?: string[]
}

interface MatrixData {
  features: Feature[]
  total: number
}

const STAGES = ['acquisition', 'activation', 'retention', 'referral', 'revenue']
const STAGE_COLORS = {
  acquisition: 'bg-blue-500',
  activation: 'bg-green-500', 
  retention: 'bg-purple-500',
  referral: 'bg-pink-500',
  revenue: 'bg-yellow-500'
}

const IMPACT_COLORS = {
  none: 'bg-gray-200 text-gray-600',
  low: 'bg-yellow-200 text-yellow-800',
  medium: 'bg-orange-200 text-orange-800', 
  high: 'bg-red-200 text-red-800'
}

const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-700',
  medium: 'bg-blue-100 text-blue-700',
  high: 'bg-red-100 text-red-700'
}

function MatrixContent() {
  const [data, setData] = useState<MatrixData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('')
  const { selectedFeatureId, openDetails, closeDetails, isDetailsOpen } = useSplitView()

  const [aiAnalysisLoading, setAiAnalysisLoading] = useState(false)
  const [aiAnalysisProgress, setAiAnalysisProgress] = useState<{
    current: number
    total: number
    currentFeature: string
  } | null>(null)
  const [aiAnalysisResult, setAiAnalysisResult] = useState<{
    success: boolean
    message: string
    analyzed: number
    meaningfulRelationships?: number
    totalAnalyzed?: number
  } | null>(null)

  useEffect(() => {
    fetchMatrixData()
  }, [priorityFilter, difficultyFilter])

  const fetchMatrixData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (priorityFilter) params.append('priority', priorityFilter)
      if (difficultyFilter) params.append('difficulty', difficultyFilter)
      
      const response = await fetch(`/api/analytics/matrix?${params}`)
      if (!response.ok) throw new Error('Failed to fetch matrix data')
      
      const result = await response.json()
      setData(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const filteredFeatures = data?.features?.filter(feature =>
    feature.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    feature.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    feature.category.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const getImpactForStage = (feature: Feature, stage: string) => {
    const impact = feature.stage_impacts?.find(si => si.stage === stage)
    return impact?.impact_level || 'none'
  }

  const getImpactScore = (feature: Feature) => {
    const scores = { none: 0, low: 1, medium: 2, high: 3 }
    return feature.stage_impacts?.reduce((sum, impact) => 
      sum + scores[impact.impact_level], 0) || 0
  }

  // Sort features by impact score (breadth of impact)
  const sortedFeatures = [...filteredFeatures].sort((a, b) =>
    getImpactScore(b) - getImpactScore(a)
  )

  // AI Analysis functions
  const runAIAnalysis = async () => {
    try {
      setAiAnalysisLoading(true)
      setAiAnalysisResult(null)
      setAiAnalysisProgress({ current: 0, total: filteredFeatures.length, currentFeature: 'Starting...' })

      // Generate session ID for progress tracking
      const sessionId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Check OpenAI connection first
      const healthResponse = await fetch('/api/ai-analysis')
      if (!healthResponse.ok) {
        throw new Error('AI service not available')
      }

      const healthData = await healthResponse.json()
      if (!healthData.data.openai_connected) {
        throw new Error('OpenAI connection failed')
      }

      // Start polling for progress updates
      const progressInterval = setInterval(async () => {
        try {
          const progressResponse = await fetch(`/api/ai-logs?session_id=${sessionId}`)
          if (progressResponse.ok) {
            const progressData = await progressResponse.json()
            const logs = progressData.data || []

            if (logs.length > 0) {
              const latestLog = logs[0]

              if (latestLog.status === 'in_progress') {
                setAiAnalysisProgress({
                  current: latestLog.current_progress || 0,
                  total: latestLog.total_progress || filteredFeatures.length,
                  currentFeature: latestLog.current_feature_name || 'Processing...'
                })
              } else if (latestLog.status === 'completed') {
                clearInterval(progressInterval)
                setAiAnalysisResult({
                  success: true,
                  message: `Analysis completed: ${latestLog.feature_count} features analyzed`,
                  analyzed: latestLog.feature_count || 0,
                  meaningfulRelationships: latestLog.meaningful_relationships_found || 0,
                  totalAnalyzed: latestLog.total_relationships_analyzed || 0
                })
                setAiAnalysisLoading(false)
                setAiAnalysisProgress(null)
                // Refresh matrix data to show updated impacts
                fetchMatrixData()
              } else if (latestLog.status === 'failed') {
                clearInterval(progressInterval)
                setAiAnalysisResult({
                  success: false,
                  message: latestLog.error_message || 'Analysis failed',
                  analyzed: 0
                })
                setAiAnalysisLoading(false)
                setAiAnalysisProgress(null)
              }
            }
          }
        } catch (progressError) {
          console.error('Progress polling error:', progressError)
        }
      }, 1000) // Poll every second

      // Run batch analysis with enhanced configuration
      const analysisResponse = await fetch('/api/ai-analysis?mode=batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feature_ids: filteredFeatures.map(f => f.id),
          session_id: sessionId,
          limit: 50,
          min_confidence_score: 60,
          min_relationship_strength: 'moderate',
          require_high_impact: true,
          max_relationships_per_feature: 3
        })
      })

      if (!analysisResponse.ok) {
        clearInterval(progressInterval)
        const errorData = await analysisResponse.json()
        throw new Error(errorData.error || 'Analysis failed')
      }

      const result = await analysisResponse.json()

      // Clear interval and handle final result
      clearInterval(progressInterval)

      setAiAnalysisResult({
        success: true,
        message: result.message || 'Analysis completed successfully',
        analyzed: result.data.analyzed_features || 0,
        meaningfulRelationships: result.data.meaningful_relationships || 0,
        totalAnalyzed: result.data.analysis_results?.reduce((sum: number, r: any) => sum + (r.total_analyzed || 0), 0) || 0
      })

      setAiAnalysisLoading(false)
      setAiAnalysisProgress(null)

      // Refresh matrix data to show updated impacts
      await fetchMatrixData()

    } catch (error) {
      console.error('AI Analysis error:', error)
      setAiAnalysisResult({
        success: false,
        message: error instanceof Error ? error.message : 'Analysis failed',
        analyzed: 0
      })
      setAiAnalysisLoading(false)
      setAiAnalysisProgress(null)
    }
  }

  const dismissAnalysisResult = () => {
    setAiAnalysisResult(null)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neo-cyan mx-auto mb-4"></div>
          <p className="text-gray-600">Loading matrix data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <button 
            onClick={fetchMatrixData}
            className="btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <SplitView
      detailsComponent={selectedFeatureId ? <FeatureDetails featureId={selectedFeatureId} compact /> : undefined}
      onCloseDetails={closeDetails}
      className="min-h-screen bg-gray-50"
    >
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <div className="py-3 border-b border-gray-100">
            <Breadcrumbs />
          </div>

          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Home
              </Link>
              <div className="ml-6 h-6 w-px bg-gray-300"></div>
              <h1 className="ml-6 text-xl font-semibold text-gray-900">
                AARRR Impact Matrix
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/strategic-themes"
                className="btn-primary flex items-center text-sm"
              >
                <Target className="w-4 h-4 mr-2" />
                Strategic Themes
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search features..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white placeholder-gray-400"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white"
              >
                <option value="">All Priorities</option>
                <option value="high">High Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="low">Low Priority</option>
              </select>

              <select
                value={difficultyFilter}
                onChange={(e) => setDifficultyFilter(e.target.value)}
                className="px-4 py-2 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neo-cyan focus:border-transparent bg-gray-800 text-white"
              >
                <option value="">All Difficulties</option>
                <option value="low">Low Difficulty</option>
                <option value="medium">Medium Difficulty</option>
                <option value="high">High Difficulty</option>
              </select>

              {/* AI Analysis Button - Temporarily Hidden */}
              {/* <button
                onClick={runAIAnalysis}
                disabled={aiAnalysisLoading || filteredFeatures.length === 0}
                className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {aiAnalysisLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Brain className="w-4 h-4" />
                )}
                {aiAnalysisLoading ? 'Analyzing...' : 'AI Analysis'}
              </button> */}

              {/* AI Logs Button - Temporarily Hidden */}
              {/* <Link
                href="/ai-logs"
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <FileText className="w-4 h-4" />
                AI Logs
              </Link> */}
            </div>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            Showing {sortedFeatures.length} features sorted by impact breadth
          </div>
        </div>

        {/* AI Analysis Progress */}
        {aiAnalysisProgress && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
              <div className="flex-1">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-blue-900">
                    🧠 AI Analysis in Progress...
                  </span>
                  <span className="text-sm text-blue-700 font-mono">
                    {aiAnalysisProgress.current} / {aiAnalysisProgress.total}
                    ({Math.round((aiAnalysisProgress.current / aiAnalysisProgress.total) * 100)}%)
                  </span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-3 mb-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${(aiAnalysisProgress.current / aiAnalysisProgress.total) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-blue-700">
                    📊 Analyzing: <span className="font-medium">{aiAnalysisProgress.currentFeature}</span>
                  </p>
                  <p className="text-xs text-blue-600">
                    Finding meaningful relationships...
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* AI Analysis Result */}
        {aiAnalysisResult && (
          <div className={`border rounded-lg p-4 mb-6 ${
            aiAnalysisResult.success
              ? 'bg-green-50 border-green-200'
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {aiAnalysisResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-600" />
                )}
                <div>
                  <p className={`font-medium ${
                    aiAnalysisResult.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {aiAnalysisResult.success ? 'Analysis Complete' : 'Analysis Failed'}
                  </p>
                  <p className={`text-sm ${
                    aiAnalysisResult.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {aiAnalysisResult.message}
                  </p>
                  {aiAnalysisResult.success && (
                    <div className="mt-2 text-xs text-green-600 space-y-1">
                      {aiAnalysisResult.analyzed > 0 && (
                        <div>📊 Features analyzed: {aiAnalysisResult.analyzed}</div>
                      )}
                      {aiAnalysisResult.meaningfulRelationships !== undefined && (
                        <div>🎯 Meaningful relationships found: {aiAnalysisResult.meaningfulRelationships}</div>
                      )}
                      {aiAnalysisResult.totalAnalyzed !== undefined && (
                        <div>🔍 Total stage impacts evaluated: {aiAnalysisResult.totalAnalyzed}</div>
                      )}
                      <div>✨ Only high-confidence relationships were stored</div>
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={dismissAnalysisResult}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Matrix Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Feature
                  </th>
                  {STAGES.map(stage => (
                    <th key={stage} className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex flex-col items-center">
                        <div className={`w-3 h-3 rounded-full ${STAGE_COLORS[stage as keyof typeof STAGE_COLORS]} mb-1`}></div>
                        {stage}
                      </div>
                    </th>
                  ))}
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedFeatures.map((feature) => (
                  <tr key={feature.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-start">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {feature.name}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {feature.description.substring(0, 100)}...
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${PRIORITY_COLORS[feature.priority]}`}>
                              {feature.priority}
                            </span>
                            <span className="text-xs text-gray-500">
                              {feature.category}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    {STAGES.map(stage => {
                      const impact = getImpactForStage(feature, stage)
                      return (
                        <td key={stage} className="px-3 py-4 text-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${IMPACT_COLORS[impact]}`}>
                            {impact}
                          </span>
                        </td>
                      )
                    })}
                    <td className="px-6 py-4 text-center">
                      <button
                        onClick={() => openDetails(feature.id)}
                        className="text-neo-cyan hover:text-neo-cyan-dark inline-flex items-center justify-center p-1 rounded hover:bg-neo-cyan/10 transition-colors"
                      >
                        <Eye className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      </div>
    </SplitView>
  )
}

export default function MatrixPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neo-cyan mx-auto mb-4"></div>
          <p className="text-gray-600">Loading matrix...</p>
        </div>
      </div>
    }>
      <MatrixContent />
    </Suspense>
  )
}
