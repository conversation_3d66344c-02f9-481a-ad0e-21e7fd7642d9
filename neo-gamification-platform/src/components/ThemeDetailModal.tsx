'use client'

import { useState } from 'react'
import { 
  X, 
  Target, 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Star,
  Zap,
  Shield,
  BarChart3
} from 'lucide-react'
import { StrategicTheme } from '@/lib/types/themes'

interface ThemeDetailModalProps {
  theme: StrategicTheme | null
  isOpen: boolean
  onClose: () => void
}

export default function ThemeDetailModal({ theme, isOpen, onClose }: ThemeDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'features' | 'timeline' | 'metrics' | 'risks'>('overview')

  if (!isOpen || !theme) return null

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Target },
    { id: 'features', label: 'Features', icon: Star },
    { id: 'timeline', label: 'Timeline', icon: Calendar },
    { id: 'metrics', label: 'Metrics', icon: BarChart3 },
    { id: 'risks', label: 'Risks', icon: AlertTriangle }
  ]

  const priorityColors = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800'
  }

  const riskColors = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800'
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-neo-dark text-white p-6 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">{theme.name}</h2>
            <p className="text-neo-cyan mt-1">{theme.corePhilosophy}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-neo-cyan text-neo-cyan'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Strategic Approach */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-neo-cyan" />
                  Strategic Approach
                </h3>
                <p className="text-gray-700">{theme.strategicApproach}</p>
              </div>

              {/* Target User Segments */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Users className="w-5 h-5 mr-2 text-neo-cyan" />
                  Target User Segments
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {theme.targetUserSegments.map((segment, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-900">{segment.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{segment.description}</p>
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">Demographics: {segment.demographics}</p>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs font-medium text-gray-700">Key Behaviors:</p>
                        <ul className="text-xs text-gray-600 list-disc list-inside">
                          {segment.behaviors.slice(0, 3).map((behavior, idx) => (
                            <li key={idx}>{behavior}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AARRR Stage Focus */}
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-neo-cyan" />
                  AARRR Stage Focus
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {theme.aarrStageFocus.map((stage, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900 capitalize">{stage.stage}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          stage.impactLevel === 'primary' ? 'bg-green-100 text-green-800' :
                          stage.impactLevel === 'secondary' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {stage.impactLevel}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{stage.expectedImprovement}</p>
                      <div className="text-xs text-gray-500">
                        <p className="font-medium">Key Metrics:</p>
                        <ul className="list-disc list-inside">
                          {stage.keyMetrics.slice(0, 2).map((metric, idx) => (
                            <li key={idx}>{metric}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Business Impact */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="card bg-green-50 border-green-200">
                  <h4 className="font-semibold text-green-900 mb-2 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Expected ROI
                  </h4>
                  <p className="text-2xl font-bold text-green-800">{theme.expectedROI}</p>
                </div>
                <div className="card bg-blue-50 border-blue-200">
                  <h4 className="font-semibold text-blue-900 mb-2 flex items-center">
                    <DollarSign className="w-4 h-4 mr-2" />
                    Estimated Cost
                  </h4>
                  <p className="text-2xl font-bold text-blue-800">{theme.totalEstimatedCost}</p>
                </div>
                <div className="card bg-purple-50 border-purple-200">
                  <h4 className="font-semibold text-purple-900 mb-2 flex items-center">
                    <Star className="w-4 h-4 mr-2" />
                    Features
                  </h4>
                  <p className="text-2xl font-bold text-purple-800">{theme.keyFeatures.length}</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'features' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold mb-4">Key Features Bundle</h3>
              <div className="grid grid-cols-1 gap-4">
                {theme.keyFeatures.map((feature, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{feature.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${priorityColors[feature.priority]}`}>
                            {feature.priority} priority
                          </span>
                          <span className="text-xs text-gray-500">
                            Effort: {feature.estimatedEffort}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <p className="text-sm text-gray-700">
                        <span className="font-medium">Expected Impact:</span> {feature.expectedImpact}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'timeline' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold mb-4">Implementation Timeline</h3>
              <div className="space-y-4">
                {theme.implementationTimeline.map((phase, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900">
                        Phase {phase.phase}: {phase.name}
                      </h4>
                      <span className="text-sm text-gray-600 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {phase.duration}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-700 mb-1">Deliverables:</p>
                        <ul className="text-gray-600 list-disc list-inside">
                          {phase.deliverables.map((deliverable, idx) => (
                            <li key={idx}>{deliverable}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium text-gray-700 mb-1">Dependencies:</p>
                        <ul className="text-gray-600 list-disc list-inside">
                          {phase.dependencies.map((dependency, idx) => (
                            <li key={idx}>{dependency}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium text-gray-700 mb-1">Resources:</p>
                        <ul className="text-gray-600 list-disc list-inside">
                          {phase.resources.map((resource, idx) => (
                            <li key={idx}>{resource}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'metrics' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">Success Metrics</h3>
              <div className="grid grid-cols-1 gap-4">
                {theme.successMetrics.map((metric, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{metric.metric}</h4>
                        <p className="text-sm text-gray-600 mt-1">Category: {metric.category}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm">
                          <div>
                            <p className="font-medium text-gray-700">Baseline</p>
                            <p className="text-gray-600">{metric.baseline}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Target</p>
                            <p className="text-gray-600">{metric.target}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Timeframe</p>
                            <p className="text-gray-600">{metric.timeframe}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Measurement</p>
                            <p className="text-gray-600">{metric.measurement}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'risks' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold mb-4">Risk Assessment</h3>
              <div className="grid grid-cols-1 gap-4">
                {theme.riskAssessment.map((risk, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 flex-1">{risk.risk}</h4>
                      <div className="flex space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${riskColors[risk.probability]}`}>
                          {risk.probability} probability
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${riskColors[risk.impact]}`}>
                          {risk.impact} impact
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-700 mb-1 flex items-center">
                          <Shield className="w-4 h-4 mr-1" />
                          Mitigation Strategy
                        </p>
                        <p className="text-gray-600">{risk.mitigation}</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-700 mb-1 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          Contingency Plan
                        </p>
                        <p className="text-gray-600">{risk.contingency}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <p><span className="font-medium">Competitive Advantage:</span> {theme.competitiveAdvantage}</p>
            <p><span className="font-medium">Market Differentiation:</span> {theme.marketDifferentiation}</p>
          </div>
          <button
            onClick={onClose}
            className="btn-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
