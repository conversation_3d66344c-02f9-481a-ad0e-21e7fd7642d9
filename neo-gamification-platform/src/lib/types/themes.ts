// Strategic Theme Types for CEO Decision Making

export interface TargetUserSegment {
  name: string
  description: string
  demographics: string
  behaviors: string[]
  painPoints: string[]
}

export interface FeatureBundle {
  featureId: string
  name: string
  description: string
  priority: 'high' | 'medium' | 'low'
  estimatedEffort: string
  expectedImpact: string
}

export interface AAARRStageFocus {
  stage: 'acquisition' | 'activation' | 'retention' | 'referral' | 'revenue'
  impactLevel: 'primary' | 'secondary' | 'minimal'
  expectedImprovement: string
  keyMetrics: string[]
}

export interface ImplementationPhase {
  phase: number
  name: string
  duration: string
  deliverables: string[]
  dependencies: string[]
  resources: string[]
}

export interface SuccessMetric {
  category: 'engagement' | 'conversion' | 'retention' | 'revenue' | 'satisfaction'
  metric: string
  baseline: string
  target: string
  timeframe: string
  measurement: string
}

export interface ResourceRequirement {
  category: 'development' | 'design' | 'product' | 'data' | 'marketing'
  role: string
  effort: string
  skills: string[]
  duration: string
}

export interface RiskAssessment {
  risk: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation: string
  contingency: string
}

export interface StrategicTheme {
  id: string
  name: string
  corePhilosophy: string
  strategicApproach: string
  targetUserSegments: TargetUserSegment[]
  keyFeatures: FeatureBundle[]
  aarrStageFocus: AAARRStageFocus[]
  implementationTimeline: ImplementationPhase[]
  successMetrics: SuccessMetric[]
  resourceRequirements: ResourceRequirement[]
  riskAssessment: RiskAssessment[]
  totalEstimatedCost: string
  expectedROI: string
  competitiveAdvantage: string
  marketDifferentiation: string
}

export interface ThemeComparison {
  dimension: string
  theme1Value: string
  theme2Value: string
  theme3Value: string
  analysis: string
}

export interface ThemeAnalysisResult {
  themes: StrategicTheme[]
  comparison: ThemeComparison[]
  recommendation: string
  executiveSummary: string
  nextSteps: string[]
  generatedAt: string
  confidence: number
}

// Input data for theme generation
export interface FeatureAnalysisInput {
  id: string
  name: string
  description: string
  category: string
  stage: string
  priority: string
  difficulty: string
  research_backed: boolean
  justification?: string
  examples?: Array<{
    company_name: string
    success_story: string
    implementation_description?: string
    metrics_before?: string
    metrics_after?: string
  }>
  mechanics?: string[]
  impact_areas?: string[]
  stage_impacts?: Array<{
    stage: string
    impact_level: 'none' | 'low' | 'medium' | 'high'
    confidence: number
    reasoning: string
  }>
}

export interface ThemeGenerationConfig {
  numberOfThemes: number
  focusAreas: string[]
  businessObjectives: string[]
  constraints: string[]
  timeHorizon: string
  budgetRange: string
  customPrompt?: string
}
