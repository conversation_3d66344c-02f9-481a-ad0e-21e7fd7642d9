import { createClient } from '@/lib/supabase/server'
import { getCurrentUserProfile } from '@/lib/auth'

export interface AILogEntry {
  session_id: string
  operation_type: 'health_check' | 'single_analysis' | 'batch_analysis' | 'strategic_theme_generation'
  status: 'started' | 'in_progress' | 'completed' | 'failed'
  feature_ids?: string[]
  feature_count?: number
  analysis_config?: any
  current_progress?: number
  total_progress?: number
  current_feature_name?: string
  meaningful_relationships_found?: number
  total_relationships_analyzed?: number
  openai_model_used?: string
  error_message?: string
  error_code?: string
  openai_request_id?: string
  themes_generated?: number
  custom_prompt?: string
  generation_config?: any
  confidence_score?: number
}

class AILogger {
  private static instance: AILogger
  
  static getInstance(): AILogger {
    if (!AILogger.instance) {
      AILogger.instance = new AILogger()
    }
    return AILogger.instance
  }

  async createLog(entry: Partial<AILogEntry>): Promise<string | null> {
    try {
      const supabase = await createClient()
      const profile = await getCurrentUserProfile()

      const { data, error } = await supabase
        .from('ai_analysis_logs')
        .insert({
          session_id: entry.session_id || `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          operation_type: entry.operation_type || 'batch_analysis',
          status: entry.status || 'started',
          user_id: profile?.id,
          feature_ids: entry.feature_ids,
          feature_count: entry.feature_count || 0,
          analysis_config: entry.analysis_config,
          current_progress: entry.current_progress || 0,
          total_progress: entry.total_progress || 0,
          current_feature_name: entry.current_feature_name,
          meaningful_relationships_found: entry.meaningful_relationships_found || 0,
          total_relationships_analyzed: entry.total_relationships_analyzed || 0,
          openai_model_used: entry.openai_model_used,
          error_message: entry.error_message,
          error_code: entry.error_code,
          openai_request_id: entry.openai_request_id,
          themes_generated: entry.themes_generated,
          custom_prompt: entry.custom_prompt,
          generation_config: entry.generation_config,
          confidence_score: entry.confidence_score
        })
        .select('id')
        .single()

      if (error) {
        console.error('Failed to create AI log:', error)
        return null
      }

      return data.id
    } catch (error) {
      console.error('AI Logger error:', error)
      return null
    }
  }

  async updateLog(sessionId: string, updates: Partial<AILogEntry>): Promise<boolean> {
    try {
      const supabase = await createClient()

      const { error } = await supabase
        .from('ai_analysis_logs')
        .update({
          status: updates.status,
          current_progress: updates.current_progress,
          total_progress: updates.total_progress,
          current_feature_name: updates.current_feature_name,
          meaningful_relationships_found: updates.meaningful_relationships_found,
          total_relationships_analyzed: updates.total_relationships_analyzed,
          openai_model_used: updates.openai_model_used,
          error_message: updates.error_message,
          error_code: updates.error_code,
          openai_request_id: updates.openai_request_id,
          themes_generated: updates.themes_generated,
          custom_prompt: updates.custom_prompt,
          generation_config: updates.generation_config,
          confidence_score: updates.confidence_score
        })
        .eq('session_id', sessionId)

      if (error) {
        console.error('Failed to update AI log:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('AI Logger update error:', error)
      return false
    }
  }

  async logStart(sessionId: string, operationType: AILogEntry['operation_type'], config?: any): Promise<void> {
    await this.createLog({
      session_id: sessionId,
      operation_type: operationType,
      status: 'started',
      analysis_config: config
    })
  }

  async logProgress(sessionId: string, current: number, total: number, currentFeature?: string): Promise<void> {
    await this.updateLog(sessionId, {
      status: 'in_progress',
      current_progress: current,
      total_progress: total,
      current_feature_name: currentFeature
    })
  }

  async logSuccess(sessionId: string, results: {
    meaningfulRelationships: number
    totalAnalyzed: number
    modelUsed: string
  }): Promise<void> {
    await this.updateLog(sessionId, {
      status: 'completed',
      meaningful_relationships_found: results.meaningfulRelationships,
      total_relationships_analyzed: results.totalAnalyzed,
      openai_model_used: results.modelUsed
    })
  }

  async logError(sessionId: string, error: any): Promise<void> {
    let errorMessage = 'Unknown error'
    let errorCode = 'UNKNOWN'
    let requestId = undefined

    if (error instanceof Error) {
      errorMessage = error.message
      
      // Handle OpenAI specific errors
      if ('status' in error) {
        errorCode = `HTTP_${error.status}`
      }
      if ('code' in error) {
        errorCode = error.code
      }
      if ('requestID' in error) {
        requestId = error.requestID
      }
    }

    await this.updateLog(sessionId, {
      status: 'failed',
      error_message: errorMessage,
      error_code: errorCode,
      openai_request_id: requestId
    })
  }

  async getLogs(limit: number = 50): Promise<any[]> {
    try {
      const supabase = await createClient()

      const { data, error } = await supabase
        .from('ai_analysis_logs_summary')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Failed to fetch AI logs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('AI Logger fetch error:', error)
      return []
    }
  }

  async getLogsBySession(sessionId: string): Promise<any[]> {
    try {
      const supabase = await createClient()

      const { data, error } = await supabase
        .from('ai_analysis_logs')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Failed to fetch session logs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('AI Logger session fetch error:', error)
      return []
    }
  }
}

export const aiLogger = AILogger.getInstance()
