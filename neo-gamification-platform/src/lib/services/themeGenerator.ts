import OpenAI from 'openai'
import { 
  StrategicTheme, 
  ThemeAnalysisResult, 
  FeatureAnalysisInput, 
  ThemeGenerationConfig,
  ThemeComparison 
} from '@/lib/types/themes'

// Lazy initialization of OpenAI client
let openai: OpenAI | null = null

function getOpenAIClient(): OpenAI {
  if (!openai) {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required')
    }
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }
  return openai
}

// Default configuration for theme generation
const DEFAULT_CONFIG: ThemeGenerationConfig = {
  numberOfThemes: 3,
  focusAreas: ['user_engagement', 'revenue_growth', 'market_differentiation'],
  businessObjectives: ['increase_user_acquisition', 'improve_retention', 'boost_revenue'],
  constraints: ['development_resources', 'timeline', 'technical_complexity'],
  timeHorizon: '12_months',
  budgetRange: 'medium'
}

function createThemeGenerationPrompt(features: FeatureAnalysisInput[], config: ThemeGenerationConfig): string {
  const featuresSummary = features.map(f => ({
    name: f.name,
    description: f.description,
    category: f.category,
    stage: f.stage,
    priority: f.priority,
    difficulty: f.difficulty,
    research_backed: f.research_backed,
    mechanics: f.mechanics,
    impact_areas: f.impact_areas,
    stage_impacts: f.stage_impacts
  }))

  return `You are a strategic gamification consultant tasked with creating 3 distinct strategic themes for NEO Bank's gamification implementation. 

AVAILABLE FEATURES DATA:
${JSON.stringify(featuresSummary, null, 2)}

BUSINESS CONTEXT:
- Company: NEO Bank (Digital Banking)
- Framework: AARRR (Acquisition, Activation, Retention, Referral, Revenue)
- Target: Create 3 fundamentally different strategic approaches
- Timeline: ${config.timeHorizon}
- Budget Range: ${config.budgetRange}

${config.customPrompt ? `ADDITIONAL REQUIREMENTS:
${config.customPrompt}

Please incorporate these specific requirements into your strategic theme generation.` : ''}

REQUIREMENTS:
1. Generate exactly 3 strategic themes that are fundamentally different in approach
2. Each theme should target different user segments or business objectives
3. Themes should vary in implementation complexity, timeline, and resource requirements
4. Include 5-7 specific features from the provided data for each theme
5. Provide comprehensive analysis for CEO decision-making

OUTPUT FORMAT (JSON):
{
  "themes": [
    {
      "id": "theme_1",
      "name": "Theme Name",
      "corePhilosophy": "Core strategic philosophy",
      "strategicApproach": "Detailed strategic approach",
      "targetUserSegments": [
        {
          "name": "Segment name",
          "description": "Segment description",
          "demographics": "Age, income, tech-savviness",
          "behaviors": ["behavior1", "behavior2"],
          "painPoints": ["pain1", "pain2"]
        }
      ],
      "keyFeatures": [
        {
          "featureId": "feature_id_from_data",
          "name": "Feature name",
          "description": "Brief description",
          "priority": "high|medium|low",
          "estimatedEffort": "X weeks/months",
          "expectedImpact": "Expected business impact"
        }
      ],
      "aarrStageFocus": [
        {
          "stage": "acquisition|activation|retention|referral|revenue",
          "impactLevel": "primary|secondary|minimal",
          "expectedImprovement": "X% improvement",
          "keyMetrics": ["metric1", "metric2"]
        }
      ],
      "implementationTimeline": [
        {
          "phase": 1,
          "name": "Phase name",
          "duration": "X months",
          "deliverables": ["deliverable1"],
          "dependencies": ["dependency1"],
          "resources": ["resource1"]
        }
      ],
      "successMetrics": [
        {
          "category": "engagement|conversion|retention|revenue|satisfaction",
          "metric": "Metric name",
          "baseline": "Current value",
          "target": "Target value",
          "timeframe": "X months",
          "measurement": "How to measure"
        }
      ],
      "resourceRequirements": [
        {
          "category": "development|design|product|data|marketing",
          "role": "Role title",
          "effort": "X FTE months",
          "skills": ["skill1", "skill2"],
          "duration": "X months"
        }
      ],
      "riskAssessment": [
        {
          "risk": "Risk description",
          "probability": "low|medium|high",
          "impact": "low|medium|high",
          "mitigation": "Mitigation strategy",
          "contingency": "Contingency plan"
        }
      ],
      "totalEstimatedCost": "$X - $Y",
      "expectedROI": "X% over Y months",
      "competitiveAdvantage": "Key competitive advantage",
      "marketDifferentiation": "How this differentiates in market"
    }
  ],
  "comparison": [
    {
      "dimension": "Comparison dimension",
      "theme1Value": "Value for theme 1",
      "theme2Value": "Value for theme 2", 
      "theme3Value": "Value for theme 3",
      "analysis": "Analysis of differences"
    }
  ],
  "recommendation": "Executive recommendation",
  "executiveSummary": "High-level summary for CEO",
  "nextSteps": ["step1", "step2"],
  "confidence": 0.85
}

IMPORTANT:
- Make themes truly distinct (different target audiences, approaches, or complexity levels)
- Use only features from the provided data
- Provide realistic timelines and resource estimates
- Include specific, measurable success metrics
- Consider technical feasibility and business impact
- Ensure recommendations are actionable for a CEO`
}

export async function generateStrategicThemes(
  features: FeatureAnalysisInput[],
  config: ThemeGenerationConfig = DEFAULT_CONFIG
): Promise<ThemeAnalysisResult> {
  try {
    const prompt = createThemeGenerationPrompt(features, config)
    
    const completion = await getOpenAIClient().chat.completions.create({
      model: "gpt-4.1-mini-2025-04-14",
      messages: [
        {
          role: "system",
          content: "You are an expert strategic consultant specializing in gamification and digital banking. Provide comprehensive, realistic strategic analysis suitable for C-level decision making."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7, // Slightly higher for creative strategic thinking
      max_tokens: 4000,
      response_format: { type: "json_object" }
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No response from OpenAI')
    }

    const result = JSON.parse(response) as ThemeAnalysisResult
    
    // Add metadata
    result.generatedAt = new Date().toISOString()
    
    // Validate that we have exactly 3 themes
    if (!result.themes || result.themes.length !== 3) {
      throw new Error('Expected exactly 3 strategic themes')
    }

    return result

  } catch (error) {
    console.error('Theme generation error:', error)
    throw new Error(`Failed to generate strategic themes: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Health check function
export async function checkThemeGenerationService(): Promise<boolean> {
  try {
    const response = await getOpenAIClient().chat.completions.create({
      model: "gpt-4.1-mini-2025-04-14",
      messages: [{ role: "user", content: "Test connection" }],
      max_tokens: 5
    })
    
    return !!response.choices[0]?.message?.content
  } catch (error) {
    console.error('Theme generation service check failed:', error)
    return false
  }
}
