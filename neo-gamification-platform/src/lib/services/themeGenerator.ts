import OpenAI from 'openai'
import {
  StrategicTheme,
  ThemeAnalysisResult,
  FeatureAnalysisInput,
  ThemeGenerationConfig,
  ThemeComparison
} from '@/lib/types/themes'
import { aiLogger } from '@/lib/ai-logger'

// Lazy initialization of OpenAI client
let openai: OpenAI | null = null

function getOpenAIClient(): OpenAI {
  if (!openai) {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required')
    }
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }
  return openai
}

// Default configuration for theme generation
const DEFAULT_CONFIG: ThemeGenerationConfig = {
  numberOfThemes: 3,
  focusAreas: ['user_engagement', 'revenue_growth', 'market_differentiation'],
  businessObjectives: ['increase_user_acquisition', 'improve_retention', 'boost_revenue'],
  constraints: ['development_resources', 'timeline', 'technical_complexity'],
  timeHorizon: '12_months',
  budgetRange: 'medium'
}

function createThemeGenerationPrompt(features: FeatureAnalysisInput[], config: ThemeGenerationConfig): string {
  const featuresSummary = features.map(f => ({
    name: f.name,
    description: f.description,
    category: f.category,
    stage: f.stage,
    priority: f.priority,
    difficulty: f.difficulty,
    research_backed: f.research_backed,
    mechanics: f.mechanics,
    impact_areas: f.impact_areas,
    stage_impacts: f.stage_impacts
  }))

  return `You are a strategic gamification consultant tasked with creating 3 distinct strategic themes for NEO Bank's gamification implementation. 

AVAILABLE FEATURES DATA:
${JSON.stringify(featuresSummary, null, 2)}

BUSINESS CONTEXT:
- Company: NEO Bank (Digital Banking)
- Framework: AARRR (Acquisition, Activation, Retention, Referral, Revenue)
- Target: Create 3 fundamentally different strategic approaches
- Timeline: ${config.timeHorizon}
- Budget Range: ${config.budgetRange}

${config.customPrompt ? `ADDITIONAL REQUIREMENTS:
${config.customPrompt}

Please incorporate these specific requirements into your strategic theme generation.` : ''}

REQUIREMENTS:
1. Generate exactly 3 strategic themes that are fundamentally different
2. Each theme targets different user segments or business objectives
3. Include 5-6 specific features from the provided data per theme
4. Keep descriptions concise but comprehensive for CEO decision-making
5. Ensure all JSON is properly formatted and complete

OUTPUT FORMAT (JSON):
{
  "themes": [
    {
      "id": "theme_1",
      "name": "Theme Name",
      "corePhilosophy": "Core strategic philosophy",
      "strategicApproach": "Detailed strategic approach",
      "targetUserSegments": [
        {
          "name": "Segment name",
          "description": "Segment description",
          "demographics": "Age, income, tech-savviness",
          "behaviors": ["behavior1", "behavior2"],
          "painPoints": ["pain1", "pain2"]
        }
      ],
      "keyFeatures": [
        {
          "featureId": "feature_id_from_data",
          "name": "Feature name",
          "description": "Brief description",
          "priority": "high|medium|low",
          "estimatedEffort": "X weeks/months",
          "expectedImpact": "Expected business impact"
        }
      ],
      "aarrStageFocus": [
        {
          "stage": "acquisition|activation|retention|referral|revenue",
          "impactLevel": "primary|secondary|minimal",
          "expectedImprovement": "X% improvement",
          "keyMetrics": ["metric1", "metric2"]
        }
      ],
      "implementationTimeline": [
        {
          "phase": 1,
          "name": "Phase name",
          "duration": "X months",
          "deliverables": ["deliverable1", "deliverable2"],
          "dependencies": ["dependency1"],
          "resources": ["resource1"]
        }
      ],
      "successMetrics": [
        {
          "category": "engagement|conversion|retention|revenue|satisfaction",
          "metric": "Metric name",
          "baseline": "Current value",
          "target": "Target value",
          "timeframe": "X months",
          "measurement": "How to measure"
        }
      ],
      "resourceRequirements": [
        {
          "category": "development|design|product|data|marketing",
          "role": "Role title",
          "effort": "X FTE months",
          "skills": ["skill1", "skill2"],
          "duration": "X months"
        }
      ],
      "riskAssessment": [
        {
          "risk": "Risk description",
          "probability": "low|medium|high",
          "impact": "low|medium|high",
          "mitigation": "Mitigation strategy",
          "contingency": "Contingency plan"
        }
      ],
      "totalEstimatedCost": "$X - $Y",
      "expectedROI": "X% over Y months",
      "competitiveAdvantage": "Key competitive advantage",
      "marketDifferentiation": "How this differentiates in market"
    }
  ],
  "comparison": [
    {
      "dimension": "Comparison dimension",
      "theme1Value": "Value for theme 1",
      "theme2Value": "Value for theme 2", 
      "theme3Value": "Value for theme 3",
      "analysis": "Analysis of differences"
    }
  ],
  "recommendation": "Executive recommendation",
  "executiveSummary": "High-level summary for CEO",
  "nextSteps": ["step1", "step2"],
  "confidence": 0.85
}

IMPORTANT:
- Make themes truly distinct (different target audiences, approaches, or complexity levels)
- Use only features from the provided data
- Provide realistic timelines and resource estimates
- Include specific, measurable success metrics
- Consider technical feasibility and business impact
- Ensure recommendations are actionable for a CEO
- CRITICAL: Ensure JSON is complete and properly formatted - do not truncate any objects or arrays`
}

export async function generateStrategicThemes(
  features: FeatureAnalysisInput[],
  config: ThemeGenerationConfig = DEFAULT_CONFIG
): Promise<ThemeAnalysisResult> {
  // Create session ID for logging
  const sessionId = `theme_gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  try {
    // Start logging
    await aiLogger.createLog({
      session_id: sessionId,
      operation_type: 'strategic_theme_generation',
      status: 'started',
      feature_count: features.length,
      generation_config: config,
      custom_prompt: config.customPrompt,
      openai_model_used: 'o3-2025-04-16'
    })

    const prompt = createThemeGenerationPrompt(features, config)

    // Update progress - starting AI request
    await aiLogger.updateLog(sessionId, {
      status: 'in_progress',
      current_progress: 1,
      total_progress: 3,
      current_feature_name: 'Generating strategic themes with AI...'
    })

    const completion = await getOpenAIClient().chat.completions.create({
      model: "o3-2025-04-16",
      messages: [
        {
          role: "system",
          content: "You are an expert strategic consultant specializing in gamification and digital banking. Provide comprehensive, realistic strategic analysis suitable for C-level decision making."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7, // Slightly higher for creative strategic thinking
      max_tokens: 3500, // Reduced to prevent truncation
      response_format: { type: "json_object" }
    })

    const response = completion.choices[0]?.message?.content
    if (!response) {
      throw new Error('No response from OpenAI')
    }

    // Update progress - processing response
    await aiLogger.updateLog(sessionId, {
      current_progress: 2,
      current_feature_name: 'Processing AI response and validating themes...',
      openai_request_id: completion.id
    })

    // Clean and validate JSON response
    let cleanedResponse = response.trim()

    // Remove any markdown code blocks if present
    if (cleanedResponse.startsWith('```json')) {
      cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
    } else if (cleanedResponse.startsWith('```')) {
      cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
    }

    // Try to find JSON object boundaries
    const jsonStart = cleanedResponse.indexOf('{')
    const jsonEnd = cleanedResponse.lastIndexOf('}')

    if (jsonStart === -1 || jsonEnd === -1 || jsonStart >= jsonEnd) {
      throw new Error('Invalid JSON structure in response')
    }

    cleanedResponse = cleanedResponse.substring(jsonStart, jsonEnd + 1)

    let result: ThemeAnalysisResult
    try {
      result = JSON.parse(cleanedResponse) as ThemeAnalysisResult
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError)
      console.error('Response content:', cleanedResponse.substring(0, 1000) + '...')
      throw new Error(`Invalid JSON response from AI: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`)
    }
    
    // Validate the parsed result structure
    if (!result || typeof result !== 'object') {
      throw new Error('Invalid response structure from AI')
    }

    if (!result.themes || !Array.isArray(result.themes)) {
      throw new Error('Missing or invalid themes array in response')
    }

    if (result.themes.length !== 3) {
      throw new Error(`Expected exactly 3 strategic themes, got ${result.themes.length}`)
    }

    // Validate each theme has required fields
    for (let i = 0; i < result.themes.length; i++) {
      const theme = result.themes[i]
      if (!theme.id || !theme.name || !theme.corePhilosophy) {
        throw new Error(`Theme ${i + 1} is missing required fields (id, name, or corePhilosophy)`)
      }
      if (!theme.keyFeatures || !Array.isArray(theme.keyFeatures)) {
        throw new Error(`Theme ${i + 1} is missing or has invalid keyFeatures array`)
      }
    }

    // Add metadata
    result.generatedAt = new Date().toISOString()

    // Log successful completion
    await aiLogger.updateLog(sessionId, {
      status: 'completed',
      current_progress: 3,
      total_progress: 3,
      themes_generated: result.themes.length,
      confidence_score: result.confidence,
      current_feature_name: `Generated ${result.themes.length} strategic themes successfully`
    })

    return result

  } catch (error) {
    console.error('Theme generation error:', error)
    throw new Error(`Failed to generate strategic themes: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Health check function
export async function checkThemeGenerationService(): Promise<boolean> {
  try {
    const response = await getOpenAIClient().chat.completions.create({
      model: "gpt-4.1-mini-2025-04-14",
      messages: [{ role: "user", content: "Test connection" }],
      max_tokens: 5
    })
    
    return !!response.choices[0]?.message?.content
  } catch (error) {
    console.error('Theme generation service check failed:', error)
    return false
  }
}
